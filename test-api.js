const axios = require('axios');

async function testAPI() {
    try {
        const response = await axios.post('http://localhost:3000/api/obfuscate', {
            code: `local x = "hello world"
print(x)
local function test()
    return "test function"
end
print(test())`
        });

        console.log('API Test Results:');
        console.log('Success:', response.data.success);
        console.log('Original Size:', response.data.originalSize);
        console.log('Obfuscated Size:', response.data.obfuscatedSize);
        console.log('\nObfuscated Code:');
        console.log(response.data.obfuscated);
    } catch (error) {
        console.error('Error testing API:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

testAPI();
