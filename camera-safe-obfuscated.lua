local MswFCOBUpDtWc={"Q2FtZX@JhLh4jTy8hTy9JJyEA`!","UGxheWVycw`AA.`","UnVuU2VydmljZQAA>","SHVtYW5vaWQA.","Q2FtZXJhIFR5cGU6>_","Q2FtZXJhIFN1YmplY3Q6:","Ww%ogICAgbG9jYWwgY2FtID0gX0cuZ2V0Q2FtZXJhKCkKICAgIHByaW50KC*JDYW1lcm,EgZnJ>vbS!Bsb2Fkc3RyaW5nOiI.sIGNh%bSkKICAgIGNhb^S5GaWV_sZE9mVmlldy_A9IDcwCl0A@_","Q2FtZXJhIFBvc2l0aW>9uOgAA*","Lg4jPh8VTh8zDz4ZTi4jTw5BL08nIC0gTi9ILzU!gQ3VycmVudENhbWVyYSBFcnJvci$EA_;"};local function aCKGMeilO(JfESCPfclZkg) local OLeTJBpmXB="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=" local sVgCSwqt="" for VGUZqtkxqmW=1,#JfESCPfclZkg do local tjjBneNOg=JfESCPfclZkg:sub(VGUZqtkxqmW,VGUZqtkxqmW) if OLeTJBpmXB:find(tjjBneNOg,1,true) then sVgCSwqt=sVgCSwqt..tjjBneNOg end end return sVgCSwqt end;local function PJGSHIySb(cFTCHjSUOMAKS) local LaiRJnKPSRz="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/" local kNkvqjqdstS={} for mjPoCIinTepjI=1,#LaiRJnKPSRz do kNkvqjqdstS[LaiRJnKPSRz:sub(mjPoCIinTepjI,mjPoCIinTepjI)]=mjPoCIinTepjI-1 end local cowNSUcKG="" for KkGzAREiX=1,#cFTCHjSUOMAKS,4 do local jklPndOLcsl,HGWmLnNe,VmUAkRBN,KLjWFsLcOwweg=kNkvqjqdstS[cFTCHjSUOMAKS:sub(KkGzAREiX,KkGzAREiX)],kNkvqjqdstS[cFTCHjSUOMAKS:sub(KkGzAREiX+1,KkGzAREiX+1)],kNkvqjqdstS[cFTCHjSUOMAKS:sub(KkGzAREiX+2,KkGzAREiX+2)],kNkvqjqdstS[cFTCHjSUOMAKS:sub(KkGzAREiX+3,KkGzAREiX+3)] if jklPndOLcsl and HGWmLnNe then local VxpWaMGyd=jklPndOLcsl*4+math.floor(HGWmLnNe/16) cowNSUcKG=cowNSUcKG..string.char(VxpWaMGyd) if VmUAkRBN then VxpWaMGyd=math.fmod(HGWmLnNe,16)*16+math.floor(VmUAkRBN/4) cowNSUcKG=cowNSUcKG..string.char(VxpWaMGyd) end if KLjWFsLcOwweg then VxpWaMGyd=math.fmod(VmUAkRBN,4)*64+KLjWFsLcOwweg cowNSUcKG=cowNSUcKG..string.char(VxpWaMGyd) end end end return cowNSUcKG end;local function HSPSyHLjDoYo(s) return PJGSHIySb(aCKGMeilO(s)) end;local function SzjFNDozdW(id,...) if id==1 then return wait(...) elseif id==2 then return print(...) end end;local function CxRiXJoG() local cam = workspace.CurrentCamera local attempts = 0 while not cam and attempts < 100 do wait(0.03) cam = workspace.CurrentCamera attempts = attempts + 1 end if not cam then error("CurrentCamera not available") end return cam end;workspace.CurrentCamera = workspace.CurrentCamera or CxRiXJoG();repeat SzjFNDozdW(1)until workspace.CurrentCamera;SzjFNDozdW(2,HSPSyHLjDoYo(MswFCOBUpDtWc[1]))_G.getCamera=function()if not workspace.CurrentCamera then repeat SzjFNDozdW(1,0.1)until workspace.CurrentCamera end;return workspace.CurrentCamera end;local SvdyZcBXifzwu=game:GetService(HSPSyHLjDoYo(MswFCOBUpDtWc[2]))local OBMnikovB=game:GetService(HSPSyHLjDoYo(MswFCOBUpDtWc[3]))local POZhONhfpZJc=SvdyZcBXifzwu.LocalPlayer;local sfIkYxCZluMLb=POZhONhfpZJc.Character or POZhONhfpZJc.CharacterAdded:Wait()local hBmVMCmmxFkJs=sfIkYxCZluMLb:WaitForChild(HSPSyHLjDoYo(MswFCOBUpDtWc[4]))local xJzUfqNApRdY=_G.getCamera()xJzUfqNApRdY.CameraSubject=hBmVMCmmxFkJs;xJzUfqNApRdY.CameraType=Enum.CameraType.Custom;SzjFNDozdW(2,HSPSyHLjDoYo(MswFCOBUpDtWc[5]),xJzUfqNApRdY.CameraType)SzjFNDozdW(2,HSPSyHLjDoYo(MswFCOBUpDtWc[6]),xJzUfqNApRdY.CameraSubject)local tEhRmMTI=HSPSyHLjDoYo(MswFCOBUpDtWc[7])local oLoQfuUmtArDQ=loadstring(tEhRmMTI)if oLoQfuUmtArDQ then oLoQfuUmtArDQ()end;local tuqSoKtAFUzQ=OBMnikovB.Heartbeat:Connect(function()local ozfBnrfJ=_G.getCamera()if ozfBnrfJ and ozfBnrfJ.Parent then if tick()%2<0.016 then SzjFNDozdW(2,HSPSyHLjDoYo(MswFCOBUpDtWc[8]),ozfBnrfJ.CFrame.Position)end end end)SzjFNDozdW(2,HSPSyHLjDoYo(MswFCOBUpDtWc[9]))