const axios = require('axios');

async function testComparison() {
    const testCode = `local function calculateSum(a, b)
    local result = a + b
    print("Sum of " .. a .. " and " .. b .. " is " .. result)
    return result
end

local x = 10
local y = 20
local sum = calculateSum(x, y)

if sum > 25 then
    print("Sum is greater than 25")
else
    print("Sum is 25 or less")
end`;

    try {
        const response = await axios.post('http://localhost:3000/api/obfuscate', {
            code: testCode
        });

        console.log('=== ORIGINAL CODE ===');
        console.log(testCode);
        console.log('\n=== OBFUSCATED + MINIFIED CODE ===');
        console.log(response.data.obfuscated);
        
        console.log('\n=== STATISTICS ===');
        console.log('✅ Success:', response.data.success);
        console.log('📏 Original Size:', response.data.originalSize, 'characters');
        console.log('📦 Obfuscated Size:', response.data.obfuscatedSize, 'characters');
        console.log('📊 Compression Ratio:', response.data.compressionRatio);
        console.log('🔒 Security Level: HIGH (Base64 + Noise + Variable Renaming + Function Dispatching)');
        console.log('⚡ Minification: ENABLED (Custom Lua minifier)');
        
    } catch (error) {
        console.error('❌ Error:', error.message);
    }
}

testComparison();
