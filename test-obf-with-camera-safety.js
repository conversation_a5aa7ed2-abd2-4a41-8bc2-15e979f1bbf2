const luamin = require('./luamin.js');

// โค้ดทดสอบที่มี Camera Safety + การเรียกจากที่อื่น
const testCodeWithSafety = `
-- Camera Safety Code
repeat wait() until workspace.CurrentCamera
print("Camera พร้อมแล้ว!")

-- สร้างฟังก์ชันปลอดภัยสำหรับโค้ดอื่น
_G.getCamera = function()
    if not workspace.CurrentCamera then
        repeat wait(0.1) until workspace.CurrentCamera
    end
    return workspace.CurrentCamera
end

-- โค้ดหลักที่ใช้ Camera
local Players = game:GetService("Players")
local RunService = game:GetService("RunService")

local player = Players.LocalPlayer
local character = player.Character or player.CharacterAdded:Wait()
local humanoid = character:WaitForChild("Humanoid")

-- ใช้ Camera อย่างปลอดภัย
local camera = _G.getCamera()
camera.CameraSubject = humanoid
camera.CameraType = Enum.CameraType.Custom

print("Camera Type:", camera.CameraType)
print("Camera Subject:", camera.CameraSubject)

-- ตัวอย่างการเรียกจาก loadstring
local cameraCode = [[
    local cam = _G.getCamera()
    print("Camera from loadstring:", cam)
    cam.FieldOfView = 70
]]

local func = loadstring(cameraCode)
if func then
    func()
end

-- ลูปอัพเดท Camera
local connection = RunService.Heartbeat:Connect(function()
    local cam = _G.getCamera()
    if cam and cam.Parent then
        -- Camera operations here
        if tick() % 2 < 0.016 then
            print("Camera Position:", cam.CFrame.Position)
        end
    end
end)

print("สคริปต์ทำงานเสร็จแล้ว - ไม่มี CurrentCamera Error!")
`;

console.log('🧪 ทดสอบ Obfuscation กับ Camera Safety');
console.log('=' .repeat(50));
console.log('\n📝 โค้ดต้นฉบับ (มี Camera Safety):');
console.log(testCodeWithSafety);
console.log('\n' + '='.repeat(50) + '\n');

try {
    const obfuscated = luamin.minify(testCodeWithSafety);
    console.log('🔒 โค้ด Obfuscated (พร้อม Camera Safety):');
    console.log(obfuscated);
    
    // บันทึกไฟล์
    require('fs').writeFileSync('camera-safe-obfuscated.lua', obfuscated);
    
    // ตรวจสอบ syntax
    const issues = [];
    
    // ตรวจสอบ parentheses
    const openParens = (obfuscated.match(/\(/g) || []).length;
    const closeParens = (obfuscated.match(/\)/g) || []).length;
    if (openParens !== closeParens) {
        issues.push(`❌ Parentheses ไม่สมดุล: ${openParens} เปิด, ${closeParens} ปิด`);
    } else {
        console.log('✅ Parentheses สมดุล');
    }
    
    // ตรวจสอบ brackets
    const openBrackets = (obfuscated.match(/\[/g) || []).length;
    const closeBrackets = (obfuscated.match(/\]/g) || []).length;
    if (openBrackets !== closeBrackets) {
        issues.push(`❌ Brackets ไม่สมดุล: ${openBrackets} เปิด, ${closeBrackets} ปิด`);
    } else {
        console.log('✅ Brackets สมดุล');
    }
    
    // ตรวจสอบ braces
    const openBraces = (obfuscated.match(/\{/g) || []).length;
    const closeBraces = (obfuscated.match(/\}/g) || []).length;
    if (openBraces !== closeBraces) {
        issues.push(`❌ Braces ไม่สมดุล: ${openBraces} เปิด, ${closeBraces} ปิด`);
    } else {
        console.log('✅ Braces สมดุล');
    }
    
    // ตรวจสอบว่ามี Camera Safety หรือไม่
    if (obfuscated.includes('repeat') && obfuscated.includes('until')) {
        console.log('✅ Camera Safety Code ยังอยู่');
    } else {
        console.log('⚠️  Camera Safety Code อาจถูก obfuscate');
    }
    
    if (obfuscated.includes('_G')) {
        console.log('✅ Global Functions ยังอยู่');
    }
    
    console.log('\n📊 สถิติการ Obfuscate:');
    console.log('=' .repeat(30));
    console.log(`📏 ขนาดเดิม: ${testCodeWithSafety.length} ตัวอักษร`);
    console.log(`📏 ขนาดใหม่: ${obfuscated.length} ตัวอักษร`);
    console.log(`📈 เปลี่ยนแปลง: +${((obfuscated.length - testCodeWithSafety.length) / testCodeWithSafety.length * 100).toFixed(1)}%`);
    
    if (issues.length === 0) {
        console.log('\n🎉 การ OBFUSCATE สำเร็จ!');
        console.log('✅ ไม่มี Syntax Error');
        console.log('✅ Camera Safety ได้รับการปกป้อง');
        console.log('✅ พร้อมใช้งานใน Roblox');
        console.log('✅ ไม่จะมี CurrentCamera Error');
        console.log('\n📁 ไฟล์บันทึกแล้ว: camera-safe-obfuscated.lua');
        console.log('🚀 คัดลอกไปใช้ใน Roblox ได้เลย!');
    } else {
        console.log('\n⚠️  พบปัญหา:');
        issues.forEach(issue => console.log(issue));
    }
    
} catch (error) {
    console.error('❌ เกิดข้อผิดพลาดในการ Obfuscate:', error.message);
}
