const axios = require('axios');

async function testSimpleCode() {
    try {
        const response = await axios.post('http://localhost:3000/api/obfuscate', {
            code: `local function hello(name)
    print("Hello " .. name)
    return "Done"
end

local msg = "World"
hello(msg)`
        });

        console.log('=== ORIGINAL CODE ===');
        console.log(`local function hello(name)
    print("Hello " .. name)
    return "Done"
end

local msg = "World"
hello(msg)`);
        
        console.log('\n=== OBFUSCATED CODE ===');
        console.log(response.data.obfuscated);
        
        console.log('\n=== STATS ===');
        console.log('Success:', response.data.success);
        console.log('Original Size:', response.data.originalSize);
        console.log('Obfuscated Size:', response.data.obfuscatedSize);
    } catch (error) {
        console.error('Error:', error.message);
    }
}

testSimpleCode();
