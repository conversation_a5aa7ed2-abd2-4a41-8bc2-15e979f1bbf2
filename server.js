const express = require('express');
const cors = require('cors');
const path = require('path');
const luamin = require('./luamin');
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Obfuscation utility functions
class LuaObfuscator {
    // Main obfuscation function with enhanced complexity
    async obfuscate(luaCode) {
        try {
            // Step 1: Obfuscate the Lua code using luamin
            let obfuscatedCode = luamin.minify(luaCode);

            // Step 2: Apply simple text-based minification to the Lua code
            let minifiedCode = this.minifyLuaCode(obfuscatedCode);

            return {
                success: true,
                obfuscated: minifiedCode,
                originalSize: luaCode.length,
                obfuscatedSize: minifiedCode.length,
                compressionRatio: ((luaCode.length - minifiedCode.length) / luaCode.length * 100).toFixed(2) + '%'
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Simple Lua code minification
    minifyLuaCode(luaCode) {
        return luaCode
            // Remove extra whitespace but keep necessary spaces
            .replace(/\s+/g, ' ')
            // Remove spaces around operators
            .replace(/\s*([=+\-*/%<>~!])\s*/g, '$1')
            // Remove spaces around parentheses and brackets
            .replace(/\s*([(){}[\],;])\s*/g, '$1')
            // Remove spaces around dots (string concatenation)
            .replace(/\s*\.\.\s*/g, '..')
            // Remove leading and trailing whitespace
            .trim()
            // Remove unnecessary semicolons at the end of lines
            .replace(/;+/g, ';')
            // Compress multiple semicolons
            .replace(/;\s*;/g, ';');
    }
}

// Create obfuscator instance
const obfuscator = new LuaObfuscator();

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.post('/api/obfuscate', async (req, res) => {
    const { code } = req.body;

    if (!code || typeof code !== 'string') {
        return res.status(400).json({
            success: false,
            error: 'Invalid Lua code provided'
        });
    }

    try {
        const result = await obfuscator.obfuscate(code);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Internal server error: ' + error.message
        });
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`Lua Obfuscator server running on http://localhost:${PORT}`);
});

module.exports = app;