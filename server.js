const express = require('express');
const cors = require('cors');
const path = require('path');
const luamin = require('./luamin');
const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('public'));

// Obfuscation utility functions
class LuaObfuscator {
    // Main obfuscation function with enhanced complexity
    obfuscate(luaCode) {
        try {
            // Step 1: Obfuscate the Lua code using luamin
            let cleanCode = luamin.minify(luaCode);

            const finalCode = cleanCode;

            return {
                success: true,
                obfuscated: finalCode,
                originalSize: luaCode.length,
                obfuscatedSize: finalCode.length
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Create obfuscator instance
const obfuscator = new LuaObfuscator();

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.post('/api/obfuscate', (req, res) => {
    const { code } = req.body;
    
    if (!code || typeof code !== 'string') {
        return res.status(400).json({
            success: false,
            error: 'Invalid Lua code provided'
        });
    }

    const result = obfuscator.obfuscate(code);
    res.json(result);
});

// Start server
app.listen(PORT, () => {
    console.log(`Lua Obfuscator server running on http://localhost:${PORT}`);
});

module.exports = app;